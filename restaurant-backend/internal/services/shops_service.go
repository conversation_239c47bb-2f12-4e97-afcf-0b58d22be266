package services

import (
	"context"
	"fmt"

	"restaurant-backend/internal/models"
	"restaurant-backend/internal/repositories"
	"restaurant-backend/internal/types"

	"github.com/google/uuid"
	"github.com/sirupsen/logrus"
)

type ShopService struct {
	shopRepo *repositories.ShopRepository
	logger   *logrus.Logger
}

func NewShopService(shopRepo *repositories.ShopRepository, logger *logrus.Logger) *ShopService {
	return &ShopService{
		shopRepo: shopRepo,
		logger:   logger,
	}
}

// Shop methods
func (s *ShopService) GetShops(ctx context.Context, filters types.ShopFilters) (*types.ShopsResponse, error) {
	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	shops, total, err := s.shopRepo.GetShops(ctx, filters)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get shops")
		return nil, fmt.Errorf("failed to get shops: %w", err)
	}

	// Convert to response format
	shopResponses := make([]types.ShopResponse, len(shops))
	for i, shop := range shops {
		shopResponses[i] = s.convertShopToResponse(shop)
	}

	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))

	return &types.ShopsResponse{
		Data:       shopResponses,
		Total:      total,
		Page:       filters.Page,
		Limit:      filters.Limit,
		TotalPages: totalPages,
	}, nil
}

func (s *ShopService) GetShopByID(ctx context.Context, shopID uuid.UUID) (*types.ShopResponse, error) {
	shop, err := s.shopRepo.GetShopByID(ctx, shopID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get shop by ID")
		return nil, fmt.Errorf("failed to get shop: %w", err)
	}

	response := s.convertShopToResponse(*shop)
	return &response, nil
}

func (s *ShopService) GetShopBySlug(ctx context.Context, slug string) (*types.ShopResponse, error) {
	shop, err := s.shopRepo.GetShopBySlug(ctx, slug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get shop by slug")
		return nil, fmt.Errorf("failed to get shop: %w", err)
	}

	response := s.convertShopToResponse(*shop)
	return &response, nil
}

func (s *ShopService) GetShopsByOwner(ctx context.Context, ownerID uuid.UUID) ([]types.ShopResponse, error) {
	shops, err := s.shopRepo.GetShopsByOwner(ctx, ownerID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get shops by owner")
		return nil, fmt.Errorf("failed to get shops: %w", err)
	}

	responses := make([]types.ShopResponse, len(shops))
	for i, shop := range shops {
		responses[i] = s.convertShopToResponse(shop)
	}

	return responses, nil
}

func (s *ShopService) GetShopsByType(ctx context.Context, shopType string) ([]types.ShopResponse, error) {
	shops, err := s.shopRepo.GetShopsByType(ctx, shopType)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get shops by type")
		return nil, fmt.Errorf("failed to get shops: %w", err)
	}

	responses := make([]types.ShopResponse, len(shops))
	for i, shop := range shops {
		responses[i] = s.convertShopToResponse(shop)
	}

	return responses, nil
}

func (s *ShopService) CreateShop(ctx context.Context, ownerID uuid.UUID, req types.CreateShopRequest) (*types.ShopResponse, error) {
	shop := &models.Shop{
		OwnerID:       ownerID,
		Name:          req.Name,
		Slug:          req.Slug,
		Description:   req.Description,
		ShopType:      req.ShopType,
		Email:         req.Email,
		Phone:         req.Phone,
		Website:       req.Website,
		Logo:          req.Logo,
		CoverImage:    req.CoverImage,
		Address:       s.convertAddressRequestToModel(req.Address),
		CuisineType:   req.CuisineType,
		PriceRange:    req.PriceRange,
		SocialMedia:   s.convertSocialMediaRequestToModel(req.SocialMedia),
		BusinessHours: req.BusinessHours,
		Status:        "active",
		IsActive:      true,
	}

	if err := s.shopRepo.CreateShop(ctx, shop); err != nil {
		s.logger.WithError(err).Error("Failed to create shop")
		return nil, fmt.Errorf("failed to create shop: %w", err)
	}

	response := s.convertShopToResponse(*shop)
	return &response, nil
}

func (s *ShopService) UpdateShop(ctx context.Context, shopID uuid.UUID, req types.UpdateShopRequest) (*types.ShopResponse, error) {
	updates := make(map[string]interface{})

	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Description != nil {
		updates["description"] = *req.Description
	}
	if req.ShopType != nil {
		updates["shop_type"] = *req.ShopType
	}
	if req.Email != nil {
		updates["email"] = *req.Email
	}
	if req.Phone != nil {
		updates["phone"] = *req.Phone
	}
	if req.Website != nil {
		updates["website"] = *req.Website
	}
	if req.Logo != nil {
		updates["logo"] = *req.Logo
	}
	if req.CoverImage != nil {
		updates["cover_image"] = *req.CoverImage
	}
	if req.Address != nil {
		updates["address_street"] = req.Address.Street
		updates["address_city"] = req.Address.City
		updates["address_state"] = req.Address.State
		updates["address_zip_code"] = req.Address.ZipCode
		updates["address_country"] = req.Address.Country
	}
	if req.CuisineType != nil {
		updates["cuisine_type"] = *req.CuisineType
	}
	if req.PriceRange != nil {
		updates["price_range"] = *req.PriceRange
	}
	if req.SocialMedia != nil {
		updates["social_media"] = s.convertSocialMediaRequestToModel(*req.SocialMedia)
	}
	if req.BusinessHours != nil {
		updates["business_hours"] = *req.BusinessHours
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}

	shop, err := s.shopRepo.UpdateShop(ctx, shopID, updates)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update shop")
		return nil, fmt.Errorf("failed to update shop: %w", err)
	}

	response := s.convertShopToResponse(*shop)
	return &response, nil
}

func (s *ShopService) DeleteShop(ctx context.Context, shopID uuid.UUID) error {
	if err := s.shopRepo.DeleteShop(ctx, shopID); err != nil {
		s.logger.WithError(err).Error("Failed to delete shop")
		return fmt.Errorf("failed to delete shop: %w", err)
	}

	return nil
}

// Branch methods
func (s *ShopService) GetBranches(ctx context.Context, shopID uuid.UUID, filters types.BranchFilters) (*types.BranchesResponse, error) {
	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	branches, total, err := s.shopRepo.GetBranches(ctx, shopID, filters)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get branches")
		return nil, fmt.Errorf("failed to get branches: %w", err)
	}

	// Convert to response format
	branchResponses := make([]types.BranchResponse, len(branches))
	for i, branch := range branches {
		branchResponses[i] = s.convertBranchToResponse(branch)
	}

	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))

	return &types.BranchesResponse{
		Data:       branchResponses,
		Total:      total,
		Page:       filters.Page,
		Limit:      filters.Limit,
		TotalPages: totalPages,
	}, nil
}

func (s *ShopService) GetBranchByID(ctx context.Context, shopID, branchID uuid.UUID) (*types.BranchResponse, error) {
	branch, err := s.shopRepo.GetBranchByID(ctx, shopID, branchID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get branch by ID")
		return nil, fmt.Errorf("failed to get branch: %w", err)
	}

	response := s.convertBranchToResponse(*branch)
	return &response, nil
}

func (s *ShopService) GetBranchBySlug(ctx context.Context, shopSlug, branchSlug string) (*types.BranchResponse, error) {
	branch, err := s.shopRepo.GetBranchBySlug(ctx, shopSlug, branchSlug)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get branch by slug")
		return nil, fmt.Errorf("failed to get branch: %w", err)
	}

	response := s.convertBranchToResponse(*branch)
	return &response, nil
}

func (s *ShopService) CreateBranch(ctx context.Context, shopID uuid.UUID, req types.CreateBranchRequest) (*types.BranchResponse, error) {
	branch := &models.ShopBranch{
		ShopID:        shopID,
		Name:          req.Name,
		Slug:          req.Slug,
		Email:         req.Email,
		Phone:         req.Phone,
		Address:       s.convertAddressRequestToModel(req.Address),
		BusinessHours: req.BusinessHours,
		Timezone:      req.Timezone,
		Status:        "active",
		IsActive:      true,
	}

	if err := s.shopRepo.CreateBranch(ctx, branch); err != nil {
		s.logger.WithError(err).Error("Failed to create branch")
		return nil, fmt.Errorf("failed to create branch: %w", err)
	}

	// Create default floor for the new branch
	floor := &models.Floor{
		BranchID:    branch.ID,
		Name:        "Ground Floor",
		Description: "Main dining area",
		Order:       1,
		Layout: models.FloorLayout{
			Width:    800,
			Height:   600,
			GridSize: 20,
			ShowGrid: true,
		},
		IsActive: true,
	}

	if err := s.shopRepo.CreateFloor(ctx, floor); err != nil {
		s.logger.WithError(err).Error("Failed to create default floor")
		return nil, fmt.Errorf("failed to create default floor: %w", err)
	}

	// Create default area for the new branch
	area := &models.TableArea{
		BranchID:    branch.ID,
		FloorID:     &floor.ID,
		Name:        "Main Dining Area",
		Description: "Primary seating area",
		Color:       "#8a745c",
		IsActive:    true,
	}

	if err := s.shopRepo.CreateTableArea(ctx, area); err != nil {
		s.logger.WithError(err).Error("Failed to create default area")
		return nil, fmt.Errorf("failed to create default area: %w", err)
	}

	response := s.convertBranchToResponse(*branch)
	return &response, nil
}

func (s *ShopService) UpdateBranch(ctx context.Context, branchID uuid.UUID, req types.UpdateBranchRequest) (*types.BranchResponse, error) {
	updates := make(map[string]interface{})

	if req.Name != nil {
		updates["name"] = *req.Name
	}
	if req.Email != nil {
		updates["email"] = *req.Email
	}
	if req.Phone != nil {
		updates["phone"] = *req.Phone
	}
	if req.Address != nil {
		updates["address_street"] = req.Address.Street
		updates["address_city"] = req.Address.City
		updates["address_state"] = req.Address.State
		updates["address_zip_code"] = req.Address.ZipCode
		updates["address_country"] = req.Address.Country
	}
	if req.BusinessHours != nil {
		updates["business_hours"] = *req.BusinessHours
	}
	if req.Timezone != nil {
		updates["timezone"] = *req.Timezone
	}
	if req.Status != nil {
		updates["status"] = *req.Status
	}
	if req.IsActive != nil {
		updates["is_active"] = *req.IsActive
	}

	branch, err := s.shopRepo.UpdateBranch(ctx, branchID, updates)
	if err != nil {
		s.logger.WithError(err).Error("Failed to update branch")
		return nil, fmt.Errorf("failed to update branch: %w", err)
	}

	response := s.convertBranchToResponse(*branch)
	return &response, nil
}

func (s *ShopService) DeleteBranch(ctx context.Context, branchID uuid.UUID) error {
	if err := s.shopRepo.DeleteBranch(ctx, branchID); err != nil {
		s.logger.WithError(err).Error("Failed to delete branch")
		return fmt.Errorf("failed to delete branch: %w", err)
	}

	return nil
}

// Combined methods
func (s *ShopService) GetShopsWithBranches(ctx context.Context, filters types.ShopFilters) (*types.ShopsResponse, error) {
	// Set default pagination
	if filters.Page == 0 {
		filters.Page = 1
	}
	if filters.Limit == 0 {
		filters.Limit = 20
	}

	shops, total, err := s.shopRepo.GetShopsWithBranches(ctx, filters)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get shops with branches")
		return nil, fmt.Errorf("failed to get shops with branches: %w", err)
	}

	// Convert to response format
	shopResponses := make([]types.ShopResponse, len(shops))
	for i, shop := range shops {
		shopResponses[i] = s.convertShopToResponse(shop)
	}

	totalPages := int((total + int64(filters.Limit) - 1) / int64(filters.Limit))

	return &types.ShopsResponse{
		Data:       shopResponses,
		Total:      total,
		Page:       filters.Page,
		Limit:      filters.Limit,
		TotalPages: totalPages,
	}, nil
}

func (s *ShopService) GetBranchWithShop(ctx context.Context, branchID uuid.UUID) (*types.BranchWithShopResponse, error) {
	branch, err := s.shopRepo.GetBranchWithShop(ctx, branchID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get branch with shop")
		return nil, fmt.Errorf("failed to get branch with shop: %w", err)
	}

	response := &types.BranchWithShopResponse{
		BranchResponse: s.convertBranchToResponse(*branch),
	}

	if branch.Shop.ID != uuid.Nil {
		response.Shop = s.convertShopToResponse(branch.Shop)
	}

	return response, nil
}

// Settings methods
func (s *ShopService) GetBranchSettings(ctx context.Context, branchID uuid.UUID) (*types.BranchSettingsResponse, error) {
	settings, err := s.shopRepo.GetBranchSettings(ctx, branchID)
	if err != nil {
		s.logger.WithError(err).Error("Failed to get branch settings")
		return nil, fmt.Errorf("failed to get branch settings: %w", err)
	}

	response := s.convertBranchSettingsToResponse(*settings)
	return &response, nil
}

func (s *ShopService) UpdateBranchSettings(ctx context.Context, branchID uuid.UUID, req types.BranchSettingsRequest) (*types.BranchSettingsResponse, error) {
	// Get current settings
	currentSettings, err := s.shopRepo.GetBranchSettings(ctx, branchID)
	if err != nil {
		return nil, fmt.Errorf("failed to get current settings: %w", err)
	}

	// Update only provided fields
	if req.Currency != nil {
		currentSettings.Currency = *req.Currency
	}
	if req.TaxRate != nil {
		currentSettings.TaxRate = *req.TaxRate
	}
	if req.ServiceChargeRate != nil {
		currentSettings.ServiceChargeRate = *req.ServiceChargeRate
	}
	if req.DefaultTipPercentage != nil {
		currentSettings.DefaultTipPercentage = *req.DefaultTipPercentage
	}
	if req.PaymentMethods != nil {
		currentSettings.PaymentMethods = *req.PaymentMethods
	}
	if req.Features != nil {
		currentSettings.Features = *req.Features
	}
	if req.Notifications != nil {
		currentSettings.Notifications = *req.Notifications
	}
	if req.Theme != nil {
		currentSettings.Theme = *req.Theme
	}
	if req.OnlineOrdering != nil {
		currentSettings.OnlineOrdering = *req.OnlineOrdering
	}
	if req.TableReservations != nil {
		currentSettings.TableReservations = *req.TableReservations
	}
	if req.DeliveryEnabled != nil {
		currentSettings.DeliveryEnabled = *req.DeliveryEnabled
	}
	if req.PickupEnabled != nil {
		currentSettings.PickupEnabled = *req.PickupEnabled
	}
	if req.MaxTableCapacity != nil {
		currentSettings.MaxTableCapacity = *req.MaxTableCapacity
	}
	if req.ReservationWindow != nil {
		currentSettings.ReservationWindow = *req.ReservationWindow
	}
	if req.MinReservationTime != nil {
		currentSettings.MinReservationTime = *req.MinReservationTime
	}

	// Update localization settings
	if req.Language != nil {
		currentSettings.Language = *req.Language
	}
	if req.Region != nil {
		currentSettings.Region = *req.Region
	}
	if req.DateFormat != nil {
		currentSettings.DateFormat = *req.DateFormat
	}
	if req.TimeFormat != nil {
		currentSettings.TimeFormat = *req.TimeFormat
	}
	if req.NumberFormat != nil {
		currentSettings.NumberFormat = *req.NumberFormat
	}
	if req.FirstDayOfWeek != nil {
		currentSettings.FirstDayOfWeek = *req.FirstDayOfWeek
	}
	if req.RTLSupport != nil {
		currentSettings.RTLSupport = *req.RTLSupport
	}
	if req.AutoDetectLanguage != nil {
		currentSettings.AutoDetectLanguage = *req.AutoDetectLanguage
	}
	if req.FallbackLanguage != nil {
		currentSettings.FallbackLanguage = *req.FallbackLanguage
	}

	// Update security settings
	if req.TwoFactorEnabled != nil {
		currentSettings.TwoFactorEnabled = *req.TwoFactorEnabled
	}
	if req.SessionTimeout != nil {
		currentSettings.SessionTimeout = *req.SessionTimeout
	}
	if req.AuditLogging != nil {
		currentSettings.AuditLogging = *req.AuditLogging
	}
	if req.DataEncryption != nil {
		currentSettings.DataEncryption = *req.DataEncryption
	}
	if req.BackupEncryption != nil {
		currentSettings.BackupEncryption = *req.BackupEncryption
	}
	if req.PasswordMinLength != nil {
		currentSettings.PasswordMinLength = *req.PasswordMinLength
	}
	if req.PasswordRequireUppercase != nil {
		currentSettings.PasswordRequireUppercase = *req.PasswordRequireUppercase
	}
	if req.PasswordRequireLowercase != nil {
		currentSettings.PasswordRequireLowercase = *req.PasswordRequireLowercase
	}
	if req.PasswordRequireNumbers != nil {
		currentSettings.PasswordRequireNumbers = *req.PasswordRequireNumbers
	}
	if req.PasswordRequireSymbols != nil {
		currentSettings.PasswordRequireSymbols = *req.PasswordRequireSymbols
	}
	if req.MaxLoginAttempts != nil {
		currentSettings.MaxLoginAttempts = *req.MaxLoginAttempts
	}
	if req.LockoutDuration != nil {
		currentSettings.LockoutDuration = *req.LockoutDuration
	}
	if req.IPWhitelist != nil {
		currentSettings.IPWhitelist = *req.IPWhitelist
	}

	// Update appearance settings
	if req.AppearanceTheme != nil {
		currentSettings.AppearanceTheme = *req.AppearanceTheme
	}
	if req.AppearanceAccentColor != nil {
		currentSettings.AppearanceAccentColor = *req.AppearanceAccentColor
	}
	if req.AppearanceFontSize != nil {
		currentSettings.AppearanceFontSize = *req.AppearanceFontSize
	}
	if req.AppearanceCustomFont != nil {
		currentSettings.AppearanceCustomFont = *req.AppearanceCustomFont
	}
	if req.AppearanceReducedMotion != nil {
		currentSettings.AppearanceReducedMotion = *req.AppearanceReducedMotion
	}
	if req.AppearanceReducedTransparency != nil {
		currentSettings.AppearanceReducedTransparency = *req.AppearanceReducedTransparency
	}
	if req.AppearanceHighContrast != nil {
		currentSettings.AppearanceHighContrast = *req.AppearanceHighContrast
	}
	if req.AppearanceCompactMode != nil {
		currentSettings.AppearanceCompactMode = *req.AppearanceCompactMode
	}

	// Update notification settings
	if req.InAppEnabled != nil {
		currentSettings.InAppEnabled = *req.InAppEnabled
	}
	if req.InAppOrders != nil {
		currentSettings.InAppOrders = *req.InAppOrders
	}
	if req.InAppReservations != nil {
		currentSettings.InAppReservations = *req.InAppReservations
	}
	if req.InAppReviews != nil {
		currentSettings.InAppReviews = *req.InAppReviews
	}
	if req.InAppStaff != nil {
		currentSettings.InAppStaff = *req.InAppStaff
	}
	if req.InAppSystem != nil {
		currentSettings.InAppSystem = *req.InAppSystem
	}
	if req.EmailEnabled != nil {
		currentSettings.EmailEnabled = *req.EmailEnabled
	}
	if req.EmailOrders != nil {
		currentSettings.EmailOrders = *req.EmailOrders
	}
	if req.EmailReservations != nil {
		currentSettings.EmailReservations = *req.EmailReservations
	}
	if req.EmailReviews != nil {
		currentSettings.EmailReviews = *req.EmailReviews
	}
	if req.EmailStaff != nil {
		currentSettings.EmailStaff = *req.EmailStaff
	}
	if req.EmailSystem != nil {
		currentSettings.EmailSystem = *req.EmailSystem
	}
	if req.EmailDigest != nil {
		currentSettings.EmailDigest = *req.EmailDigest
	}
	if req.PushEnabled != nil {
		currentSettings.PushEnabled = *req.PushEnabled
	}
	if req.PushOrders != nil {
		currentSettings.PushOrders = *req.PushOrders
	}
	if req.PushReservations != nil {
		currentSettings.PushReservations = *req.PushReservations
	}
	if req.PushReviews != nil {
		currentSettings.PushReviews = *req.PushReviews
	}
	if req.PushStaff != nil {
		currentSettings.PushStaff = *req.PushStaff
	}
	if req.PushSystem != nil {
		currentSettings.PushSystem = *req.PushSystem
	}

	if err := s.shopRepo.UpdateBranchSettings(ctx, branchID, *currentSettings); err != nil {
		s.logger.WithError(err).Error("Failed to update branch settings")
		return nil, fmt.Errorf("failed to update branch settings: %w", err)
	}

	response := s.convertBranchSettingsToResponse(*currentSettings)
	return &response, nil
}

// Helper methods
func (s *ShopService) convertShopToResponse(shop models.Shop) types.ShopResponse {
	response := types.ShopResponse{
		ID:            shop.ID,
		OwnerID:       shop.OwnerID,
		Name:          shop.Name,
		Slug:          shop.Slug,
		Description:   shop.Description,
		ShopType:      shop.ShopType,
		Email:         shop.Email,
		Phone:         shop.Phone,
		Website:       shop.Website,
		Logo:          shop.Logo,
		CoverImage:    shop.CoverImage,
		Address:       s.convertAddressToResponse(shop.Address),
		CuisineType:   shop.CuisineType,
		PriceRange:    shop.PriceRange,
		Rating:        shop.Rating,
		ReviewCount:   shop.ReviewCount,
		SocialMedia:   s.convertSocialMediaToResponse(shop.SocialMedia),
		BusinessHours: shop.BusinessHours,
		Status:        shop.Status,
		IsVerified:    shop.IsVerified,
		IsActive:      shop.IsActive,
		CreatedAt:     shop.CreatedAt,
		UpdatedAt:     shop.UpdatedAt,
	}

	// Add branches if available
	if len(shop.Branches) > 0 {
		branches := make([]types.BranchResponse, len(shop.Branches))
		for i, branch := range shop.Branches {
			branches[i] = s.convertBranchToResponse(branch)
		}
		response.Branches = branches
	}

	return response
}

func (s *ShopService) convertBranchToResponse(branch models.ShopBranch) types.BranchResponse {
	response := types.BranchResponse{
		ID:            branch.ID,
		ShopID:        branch.ShopID,
		Name:          branch.Name,
		Slug:          branch.Slug,
		Email:         branch.Email,
		Phone:         branch.Phone,
		Address:       s.convertAddressToResponse(branch.Address),
		BusinessHours: branch.BusinessHours,
		Timezone:      branch.Timezone,
		Status:        branch.Status,
		IsActive:      branch.IsActive,
		CreatedAt:     branch.CreatedAt,
		UpdatedAt:     branch.UpdatedAt,
	}

	// Add shop if available
	if branch.Shop.ID != uuid.Nil {
		shopResponse := s.convertShopToResponse(branch.Shop)
		response.Shop = &shopResponse
	}

	return response
}

func (s *ShopService) convertAddressToResponse(address models.Address) types.AddressResponse {
	return types.AddressResponse{
		Street:  address.Street,
		City:    address.City,
		State:   address.State,
		ZipCode: address.ZipCode,
		Country: address.Country,
	}
}

func (s *ShopService) convertSocialMediaToResponse(socialMedia models.SocialMediaLinks) types.SocialMediaResponse {
	return types.SocialMediaResponse{
		Facebook:  socialMedia.Facebook,
		Instagram: socialMedia.Instagram,
		Twitter:   socialMedia.Twitter,
		LinkedIn:  socialMedia.LinkedIn,
		YouTube:   socialMedia.YouTube,
		TikTok:    socialMedia.TikTok,
	}
}

func (s *ShopService) convertBranchSettingsToResponse(settings models.BranchSettings) types.BranchSettingsResponse {
	return types.BranchSettingsResponse{
		Currency:             settings.Currency,
		TaxRate:              settings.TaxRate,
		ServiceChargeRate:    settings.ServiceChargeRate,
		DefaultTipPercentage: settings.DefaultTipPercentage,
		PaymentMethods:       settings.PaymentMethods,
		Features:             settings.Features,
		Notifications:        settings.Notifications,
		Theme:                settings.Theme,
		OnlineOrdering:       settings.OnlineOrdering,
		TableReservations:    settings.TableReservations,
		DeliveryEnabled:      settings.DeliveryEnabled,
		PickupEnabled:        settings.PickupEnabled,
		MaxTableCapacity:     settings.MaxTableCapacity,
		ReservationWindow:    settings.ReservationWindow,
		MinReservationTime:   settings.MinReservationTime,

		// Localization settings
		Language:           settings.Language,
		Region:             settings.Region,
		DateFormat:         settings.DateFormat,
		TimeFormat:         settings.TimeFormat,
		NumberFormat:       settings.NumberFormat,
		FirstDayOfWeek:     settings.FirstDayOfWeek,
		RTLSupport:         settings.RTLSupport,
		AutoDetectLanguage: settings.AutoDetectLanguage,
		FallbackLanguage:   settings.FallbackLanguage,

		// Security settings
		TwoFactorEnabled:         settings.TwoFactorEnabled,
		SessionTimeout:           settings.SessionTimeout,
		AuditLogging:             settings.AuditLogging,
		DataEncryption:           settings.DataEncryption,
		BackupEncryption:         settings.BackupEncryption,
		PasswordMinLength:        settings.PasswordMinLength,
		PasswordRequireUppercase: settings.PasswordRequireUppercase,
		PasswordRequireLowercase: settings.PasswordRequireLowercase,
		PasswordRequireNumbers:   settings.PasswordRequireNumbers,
		PasswordRequireSymbols:   settings.PasswordRequireSymbols,
		MaxLoginAttempts:         settings.MaxLoginAttempts,
		LockoutDuration:          settings.LockoutDuration,
		IPWhitelist:              settings.IPWhitelist,

		// Appearance settings
		AppearanceTheme:               settings.AppearanceTheme,
		AppearanceAccentColor:         settings.AppearanceAccentColor,
		AppearanceFontSize:            settings.AppearanceFontSize,
		AppearanceCustomFont:          settings.AppearanceCustomFont,
		AppearanceReducedMotion:       settings.AppearanceReducedMotion,
		AppearanceReducedTransparency: settings.AppearanceReducedTransparency,
		AppearanceHighContrast:        settings.AppearanceHighContrast,
		AppearanceCompactMode:         settings.AppearanceCompactMode,

		// Notification settings
		InAppEnabled:      settings.InAppEnabled,
		InAppOrders:       settings.InAppOrders,
		InAppReservations: settings.InAppReservations,
		InAppReviews:      settings.InAppReviews,
		InAppStaff:        settings.InAppStaff,
		InAppSystem:       settings.InAppSystem,
		EmailEnabled:      settings.EmailEnabled,
		EmailOrders:       settings.EmailOrders,
		EmailReservations: settings.EmailReservations,
		EmailReviews:      settings.EmailReviews,
		EmailStaff:        settings.EmailStaff,
		EmailSystem:       settings.EmailSystem,
		EmailDigest:       settings.EmailDigest,
		PushEnabled:       settings.PushEnabled,
		PushOrders:        settings.PushOrders,
		PushReservations:  settings.PushReservations,
		PushReviews:       settings.PushReviews,
		PushStaff:         settings.PushStaff,
		PushSystem:        settings.PushSystem,
	}
}

func (s *ShopService) convertAddressRequestToModel(address types.AddressRequest) models.Address {
	return models.Address{
		Street:  address.Street,
		City:    address.City,
		State:   address.State,
		ZipCode: address.ZipCode,
		Country: address.Country,
	}
}

func (s *ShopService) convertSocialMediaRequestToModel(socialMedia types.SocialMediaRequest) models.SocialMediaLinks {
	return models.SocialMediaLinks{
		Facebook:  socialMedia.Facebook,
		Instagram: socialMedia.Instagram,
		Twitter:   socialMedia.Twitter,
		LinkedIn:  socialMedia.LinkedIn,
		YouTube:   socialMedia.YouTube,
		TikTok:    socialMedia.TikTok,
	}
}
